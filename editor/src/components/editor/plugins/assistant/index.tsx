import { useActions } from '@topwrite/common';
import { useEffect } from 'react';
import { useSlate } from 'slate-react';
import { ReactComponent as Icon } from '../../../../images/assistant.svg';
import useFormatMessage from '../../../../lib/use-format-message';
import { createPlatePlugin } from '../../plate-plugin';
import { calculateFilePosition } from '../../utils/position-calculator';

export const AssistantPlugin = createPlatePlugin({
    tools() {
        return {
            '-10': ({ Component }) => {
                const t = useFormatMessage();
                const editor = useSlate();
                const { setRange } = useActions('workspace');
                const { selection } = editor;

                useEffect(() => {
                    if (selection) {
                        const range = calculateFilePosition(editor, selection);
                        setRange(range);
                    } else {
                        setRange(null);
                    }

                    return () => {
                        setRange(null);
                    };
                }, [selection, editor]);

                return <Component
                    title={t('editor.tool.assistant')}
                    active
                >
                    <Icon />
                </Component>;
            }
        };
    },
});
